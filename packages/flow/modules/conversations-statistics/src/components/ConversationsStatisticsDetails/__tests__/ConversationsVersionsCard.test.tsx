import { EmbraceTestRenderer } from "@embrace-testing/tools";
import { screen, type RenderResult } from "@testing-library/react";
import { useParams } from "react-router-dom";
import { vi } from "vitest";
import useConversationDetails from "../../../hooks/useConversationDetails";
import ConversationsVersionsCard from "../ConversationsVersionsCard";

vi.mock("react-router-dom", () => ({
  useParams: vi.fn(),
}));

vi.mock("../../../hooks/useConversationDetails");

const renderWithSetup = (): RenderResult => {
  return new EmbraceTestRenderer(<ConversationsVersionsCard />).render();
};

describe("ConversationsVersionsCard", () => {
  beforeEach(() => {
    (useParams as vi.Mock).mockReturnValue({ id: "123" });
    (useConversationDetails as vi.Mock).mockReturnValue({
      loading: false,
      data: null,
      error: null,
    });
  });

  it("should display a loading message while fetching data", async () => {
    (useConversationDetails as vi.Mock).mockReturnValue({
      loading: true,
    });

    renderWithSetup();

    expect(await screen.findByText("conversations-statistics.versions.loading")).toBeInTheDocument();
  });

  it("should display an error message when data fetching fails", async () => {
    (useConversationDetails as vi.Mock).mockReturnValue({
      error: new Error("Failed to fetch"),
    });

    renderWithSetup();

    expect(await screen.findByText("conversations-statistics.versions.error.generic")).toBeInTheDocument();
  });

  it('should display a "no versions found" message when there are no versions', async () => {
    (useConversationDetails as vi.Mock).mockReturnValue({
      data: { versions: [] },
    });

    renderWithSetup();

    expect(await screen.findByText("conversations-statistics.versions.empty")).toBeInTheDocument();
  });

  it("should display a list of versions when data is fetched successfully", async () => {
    (useConversationDetails as vi.Mock).mockReturnValue({
      data: {
        versions: [
          { id: "1", version: 1, createdAt: "2023-01-01T12:00:00Z" },
          { id: "2", version: 2, createdAt: "2023-01-02T12:00:00Z" },
        ],
      },
    });

    renderWithSetup();

    expect(await screen.findByText("conversations-statistics.versions.title")).toBeInTheDocument();
    expect(screen.getByRole("list")).toBeInTheDocument();
    expect(screen.getAllByRole("listitem")).toHaveLength(2);
    expect(screen.getByText("Version 1")).toBeInTheDocument();
    expect(screen.getByText("Version 2")).toBeInTheDocument();
  });
});
